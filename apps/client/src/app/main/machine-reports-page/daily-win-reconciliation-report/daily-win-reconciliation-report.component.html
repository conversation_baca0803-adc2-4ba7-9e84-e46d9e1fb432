<tg-main-panel [contentPadding]="true" [fullHeight]="true" [scrollable]="true">
    <form [formGroup]="form" class="no-print">
        <mat-form-field>
            <mat-label i18n>Type</mat-label>
            <mat-select formControlName="type">
                @for (reportType of reportTypesArray; track reportType) {
                    <mat-option [value]="reportType">{{ reportType | reportType }}</mat-option>
                }
            </mat-select>
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Promo</mat-label>
            <mat-select formControlName="promo">
                @for (promoInclusionType of promoInclusionTypes; track promoInclusionType) {
                    <mat-option [value]="promoInclusionType">{{ promoInclusionType | winReconciliationReportType }}</mat-option>
                }
            </mat-select>
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Gaming Day</mat-label>
            <mat-date-range-input [formGroup]="form" [rangePicker]="picker">
                <input matStartDate formControlName="start" placeholder="Start date" i18n-placeholder />
                <input matEndDate formControlName="end" placeholder="End date" i18n-placeholder />
            </mat-date-range-input>
            <mat-hint i18n>DD/MM/YYYY – DD/MM/YYYY</mat-hint>
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-date-range-picker #picker></mat-date-range-picker>
            @if (form.controls.end.errors?.required) {
                <mat-error i18n>End date is required</mat-error>
            } @else if (form.controls.end.errors?.maxDaysGreaterThanOtherControl?.otherControlName === 'start') {
                <mat-error i18n>Date range cannot exceed {{ maxIntervalInDays }} days</mat-error>
            }
        </mat-form-field>

        <button mat-flat-button color="primary" [disabled]="!form.valid" (click)="generateReport()" i18n>
            <mat-icon>assessment</mat-icon>
            Generate Report
        </button>

        <button mat-flat-button color="primary" (click)="print()"><mat-icon>print</mat-icon> <span i18n>Print</span></button>
    </form>

    <div *ngIf="requestData && requestData.start && requestData.end" id="print-daily-win-reconciliation-report">
        <div class="header">
            <div i18n class="operator-row print-only">{{ operatorName }}</div>
            <div class="title-row print-only">
                <div class="title" i18n>Daily Win Reconciliation Report</div>
                <div class="generated-meta-info-container">
                    <div i18n class="label">Generated by:</div>
                    <div class="operator-and-time">{{ generatedBy }}, {{ generatedTime | tgDateTime }}</div>
                </div>
            </div>
            <div class="filters-row">
                <div class="filters print-only">
                    <div class="label-value">
                        <div i18n class="label">Gaming Day:</div>
                        <div class="value">
                            @if (requestData.start !== requestData.end) {
                                <ng-container i18n> {{ requestData.start | tgDate }} to {{ requestData.end | tgDate }} </ng-container>
                            } @else {
                                {{ requestData.start | tgDate }}
                            }
                        </div>
                    </div>
                    <div class="label-value">
                        <div i18n class="label">Promo:</div>
                        <div class="value">{{ requestData.promo }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div *ngIf="requestData" id="print-report-daily-metrics">
            <h4>
                @if (requestData.start !== requestData.end) {
                    <ng-container i18n>
                        Daily Machine Performance - from {{ requestData.start | tgDate }} to {{ requestData.end | tgDate }}
                    </ng-container>
                } @else {
                    <ng-container i18n> Daily Machine Performance - {{ requestData.start | tgDate }} </ng-container>
                }
            </h4>
            @if (dataSourceTotal && columnsSourceTotal) {
                <h5 i18n>Total</h5>
                <div class="totals-section">
                    <tg-table
                        #table
                        [enableSelection]="false"
                        [columnsSource]="columnsSourceTotal"
                        [dataSource]="dataSourceTotal"
                        #sort="matSort"
                        [matSort]="sort"
                        [showPaginator]="false"
                        matSortActive="variance"
                        matSortDirection="desc"
                        matSortDisableClear
                    >
                        <!-- Metrics with group mapping using ReconciliationAmountIconComponent -->
                        <ng-template tgColumnCell="metrics.cash-revenue" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'cash-revenue'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.ticket-revenue" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'ticket-revenue'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.transfer-revenue" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'transfer-revenue'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.total-revenue" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'total-revenue'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.ticket-expenses" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'ticket-expenses'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.transfer-expenses" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'transfer-expenses'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.accounting-handpays" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'accounting-handpays'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.accounting-total-out" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'accounting-total-out'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.play-total-win" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'play-total-win'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.turnover" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'turnover'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.paid-to-machine" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'paid-to-machine'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.jackpots" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'jackpots'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.progressives" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'progressives'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                    </tg-table>
                </div>
            }
            @if (dataSourceMachines && columnsSourceMachines) {
                <h5 i18n>Machines</h5>
                <div class="machines-section">
                    <tg-table
                        #table
                        [enableSelection]="false"
                        [columnsSource]="columnsSourceMachines"
                        [dataSource]="dataSourceMachines"
                        #sort="matSort"
                        [matSort]="sort"
                        [showPaginator]="false"
                        matSortActive="variance"
                        matSortDirection="desc"
                        matSortDisableClear
                    >
                        <!-- Metrics with group mapping using ReconciliationAmountIconComponent -->
                        <ng-template tgColumnCell="metrics.cash-revenue" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'cash-revenue'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.ticket-revenue" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'ticket-revenue'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.transfer-revenue" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'transfer-revenue'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.total-revenue" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'total-revenue'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.ticket-expenses" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'ticket-expenses'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.transfer-expenses" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'transfer-expenses'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.accounting-handpays" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'accounting-handpays'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.accounting-total-out" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'accounting-total-out'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.play-total-win" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'play-total-win'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.turnover" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'turnover'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.paid-to-machine" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'paid-to-machine'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.jackpots" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'jackpots'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                        <ng-template tgColumnCell="metrics.progressives" let-row="row">
                            <app-reconciliation-amount-icon [metricKey]="'progressives'" [metricValues]="row.metrics"></app-reconciliation-amount-icon>
                        </ng-template>
                    </tg-table>
                </div>
            }
        </div>
    </div>
    <tg-loading-overlay [visible]="isLoading" [showSpinner]="true" style="z-index: 1000"></tg-loading-overlay>
</tg-main-panel>
