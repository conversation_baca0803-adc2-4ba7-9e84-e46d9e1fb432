import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MachineWinReconciliationReportMetrics, ReconciliationReportMetrics } from '@cms/machine-metrics';
import { TgPrimitiveTextComponent } from '@tronius/frontend-ui';

@Component({
    selector: 'app-reconciliation-amount-icon',
    standalone: true,
    imports: [CommonModule, MatIconModule, TgPrimitiveTextComponent],
    template: `
        <div class="reconciliation-amount-container">
            <tg-primitive-text
                [value]="metricValues[metricKey]"
                type="currency"
                [class.text-success]="isMetricMatching"
                [class.text-danger]="!isMetricMatching">
            </tg-primitive-text>
            <mat-icon
                [class.text-success]="isMetricMatching"
                [class.text-danger]="!isMetricMatching"
                class="metric-icon">
                {{ isMetricMatching ? 'check_circle' : 'error' }}
            </mat-icon>
        </div>
    `,
    styles: [`
        .reconciliation-amount-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .metric-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
        }

        .text-success {
            color: #4caf50;
        }

        .text-danger {
            color: #f44336;
        }
    `]
})
export class ReconciliationAmountIconComponent {
    @Input() metricKey!: ReconciliationReportMetrics;
    @Input() metricValues!: MachineWinReconciliationReportMetrics;

    /**
     * Determines if the current metric matches its corresponding group metrics
     */
    get isMetricMatching(): boolean {
        const metricGroupMapping = this.getMetricGroupMapping();
        const metricGroup = metricGroupMapping[this.metricKey];

        if (!metricGroup || metricGroup.length === 0) {
            return true; // If no group mapping, consider it as matching
        }

        const currentValue = this.metricValues[this.metricKey];

        // If current value is null or undefined, consider it as not matching
        if (currentValue === null || currentValue === undefined) {
            return false;
        }

        // Check if current metric value matches any of the metrics in its group
        return metricGroup.every((groupMetricKey) => {
            const groupValue = this.metricValues[groupMetricKey];
            return groupValue !== null && groupValue !== undefined && groupValue === currentValue;
        });
    }

    /**
     * Returns the metric group mapping for reconciliation validation
     * This method defines which metrics should match for validation purposes
     */
    private getMetricGroupMapping(): Record<ReconciliationReportMetrics, ReconciliationReportMetrics[]> {
        return {
            [ReconciliationReportMetrics.CashRevenue]: [ReconciliationReportMetrics.FinanceCashIn],
            [ReconciliationReportMetrics.TicketRevenue]: [ReconciliationReportMetrics.FinanceTicketsIn],
            [ReconciliationReportMetrics.TransferRevenue]: [ReconciliationReportMetrics.FinanceTransfersIn],
            [ReconciliationReportMetrics.TotalRevenue]: [ReconciliationReportMetrics.FinanceTotalIn],
            [ReconciliationReportMetrics.TicketExpenses]: [ReconciliationReportMetrics.FinanceTicketsOut],
            [ReconciliationReportMetrics.TransferExpenses]: [ReconciliationReportMetrics.FinanceTransfersOut],
            [ReconciliationReportMetrics.AccountingHandpays]: [ReconciliationReportMetrics.FinanceHandpays],
            [ReconciliationReportMetrics.AccountingTotalOut]: [ReconciliationReportMetrics.FinanceTotalOut],

            [ReconciliationReportMetrics.PlayTotalWin]: [ReconciliationReportMetrics.FinanceTotalWin],
            [ReconciliationReportMetrics.Turnover]: [ReconciliationReportMetrics.FinanceTotalWin],
            [ReconciliationReportMetrics.PaidToMachine]: [ReconciliationReportMetrics.FinanceTotalWin],
            [ReconciliationReportMetrics.Jackpots]: [ReconciliationReportMetrics.FinanceTotalWin],
            [ReconciliationReportMetrics.Progressives]: [ReconciliationReportMetrics.FinanceTotalWin],

            // Metrics without group mapping (always considered matching)
            [ReconciliationReportMetrics.AccountingTotalWin]: [],
            [ReconciliationReportMetrics.AccountingCreditsIn]: [],
            [ReconciliationReportMetrics.AccountingCreditsOut]: [],
            [ReconciliationReportMetrics.FinanceTotalWin]: [],
            [ReconciliationReportMetrics.FinanceCashIn]: [],
            [ReconciliationReportMetrics.FinanceTicketsIn]: [],
            [ReconciliationReportMetrics.FinanceTransfersIn]: [],
            [ReconciliationReportMetrics.FinanceCreditsIn]: [],
            [ReconciliationReportMetrics.FinanceTotalIn]: [],
            [ReconciliationReportMetrics.FinanceTicketsOut]: [],
            [ReconciliationReportMetrics.FinanceTransfersOut]: [],
            [ReconciliationReportMetrics.FinanceHandpays]: [],
            [ReconciliationReportMetrics.FinanceCreditsOut]: [],
            [ReconciliationReportMetrics.FinanceTotalOut]: []
        };
    }
}
