import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Title } from '@angular/platform-browser';
import {
    MachineWinReconciliationReportItem,
    MachineWinReconciliationReportMetrics,
    ReconciliationReportMetrics,
    WinReconciliationDailyRequest,
    WinReconciliationReportType
} from '@cms/machine-metrics';
import { ErrorUtil, SettingsService, TgLoggerFactory, maxDaysGreaterThanOtherControlValidator } from '@tronius/frontend-common';
import {
    TgArrayDataSource,
    TgBrowserStorageColumnsSource,
    TgDateModule,
    TgLoadingOverlayComponent,
    TgMainPanelModule,
    TgTableModule
} from '@tronius/frontend-ui';
import { formatDate, subDays } from 'date-fns';
import { firstValueFrom } from 'rxjs';

import { BackendService } from '../../../core/backend/backend.service';
import { ReportFormsService } from '../../../core/services/report-forms.service';
import { getWinReconciliationReportColumnsSource } from '../common/column-sources/win-reconciliation-report.columns';
import { ReportTypePipe } from '../common/pipes/report-type.pipe';
import { WinReconciliationReportTypePipe } from '../common/pipes/win-reconciliation-report-type.pipe';
import { ReportType } from '../common/types/report-type.enum';

type MachineWinReconciliationReportRow = MachineWinReconciliationReportItem & {
    variance: number | null;
};

type TotalWinReconciliationReportRow = Omit<MachineWinReconciliationReportRow, 'machine'>;

interface FormGroupType {
    type: FormControl<ReportType>;
    promo: FormControl<WinReconciliationReportType>;
    start: FormControl<Date | null>;
    end: FormControl<Date | null>;
}

@Component({
    selector: 'app-daily-win-reconciliation-report',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatIconModule,
        MatDatepickerModule,
        MatSelectModule,
        MatSnackBarModule,
        TgMainPanelModule,
        TgDateModule,
        TgTableModule,
        ReportTypePipe,
        WinReconciliationReportTypePipe,
        TgLoadingOverlayComponent,
        WinReconciliationReportTypePipe
    ],
    templateUrl: './daily-win-reconciliation-report.component.html',
    styleUrls: ['./daily-win-reconciliation-report.component.scss']
})
export class DailyWinReconciliationReportComponent implements OnInit, OnDestroy {
    protected readonly reportTypesArray = Object.values(ReportType);
    protected readonly promoInclusionTypes = Object.values(WinReconciliationReportType);
    protected readonly maxIntervalInDays = 30;

    protected form!: FormGroup<FormGroupType>;
    protected requestData!: typeof this.form.value;

    protected columnsSourceMachines?: TgBrowserStorageColumnsSource;
    protected dataSourceMachines!: TgArrayDataSource<MachineWinReconciliationReportItem>;
    protected columnsSourceTotal?: TgBrowserStorageColumnsSource;
    protected dataSourceTotal!: TgArrayDataSource<TotalWinReconciliationReportRow>;

    protected readonly operatorName: string;
    protected generatedBy!: string;
    protected generatedTime = new Date();
    protected isLoading = false;

    private readonly logger = TgLoggerFactory.getLogger(DailyWinReconciliationReportComponent.name);

    constructor(
        private readonly backendService: BackendService,
        private readonly snackBar: MatSnackBar,
        private readonly reportFormsService: ReportFormsService,
        private readonly settingsService: SettingsService,
        private readonly titleService: Title
    ) {
        const title = $localize`Machine Daily Win Reconciliation Report`;
        this.generatedBy = this.reportFormsService.determineGeneratedBy();
        this.titleService.setTitle(`${title} (${this.generatedBy})`);
        this.operatorName = this.settingsService.getSettings().propertyName;
        this.form = this.initForm();
        this.requestData = this.form.value;
    }

    ngOnInit(): void {
        this.generateReport();
    }

    ngOnDestroy(): void {
        this.titleService.setTitle('CMS');
    }

    protected generateReport(): void {
        this.determineColumnsSource();
        void this.getReport();
    }

    protected print(): void {
        window.print();
    }

    private initForm(): FormGroup<FormGroupType> {
        const gamingDay = this.reportFormsService.getCurrentGamingDayData().date;
        const start = subDays(new Date(gamingDay), 1);

        const form = new FormGroup({
            type: new FormControl<ReportType>(ReportType.Win, { nonNullable: true, validators: [Validators.required] }),
            promo: new FormControl<WinReconciliationReportType>(WinReconciliationReportType.IncludePromo, {
                nonNullable: true,
                validators: [Validators.required]
            }),
            start: new FormControl<Date | null>(start, Validators.required),
            end: new FormControl<Date | null>(start, Validators.required)
        });

        form.controls.end.addValidators(maxDaysGreaterThanOtherControlValidator(form.controls.start, this.maxIntervalInDays));

        return form;
    }

    private determineColumnsSource(): void {
        const { type } = this.form.getRawValue();

        const columns = type === ReportType.Full ? 'full' : 'win';
        this.columnsSourceMachines = getWinReconciliationReportColumnsSource('daily', columns, 'machines');
        this.columnsSourceTotal = getWinReconciliationReportColumnsSource('daily', columns, 'total');
    }

    private async getReport(): Promise<void> {
        try {
            this.isLoading = true;

            const machinesReportRows = await this.getReportMachinesRows();

            this.dataSourceMachines = new TgArrayDataSource<MachineWinReconciliationReportItem>(machinesReportRows);
            this.dataSourceTotal = new TgArrayDataSource<TotalWinReconciliationReportRow>([
                this.createTotalRowFromMachineRows(machinesReportRows)
            ]);
        } catch (error) {
            const errorMessage = ErrorUtil.extractErrorMessage(error);
            this.logger.error('Failed to load daily win reconciliation report', errorMessage);
            this.snackBar.open($localize`Failed to load daily win reconciliation report`, $localize`Close`);
        } finally {
            this.isLoading = false;
        }
    }

    private async getReportMachinesRows(): Promise<MachineWinReconciliationReportRow[]> {
        const { start, end, promo } = (this.requestData = this.form.getRawValue());

        if (!start || !end) {
            return [];
        }

        const fromGamingDay = formatDate(start, 'yyyy-MM-dd');
        const toGamingDay = formatDate(end, 'yyyy-MM-dd');

        const request = WinReconciliationDailyRequest.create({ fromGamingDay, toGamingDay, type: promo });

        return (await firstValueFrom(this.backendService.getDailyMachineWinReconciliationReport(request))).map((reportItem) => ({
            ...reportItem,
            variance: this.calculateVariance(reportItem.metrics)
        }));
    }

    private createTotalRowFromMachineRows(machineRows: MachineWinReconciliationReportRow[]): TotalWinReconciliationReportRow {
        const initialMetrics: MachineWinReconciliationReportMetrics = {};
        Object.values(ReconciliationReportMetrics).forEach((metricKey) => {
            initialMetrics[metricKey] = 0;
        });

        const totalsRow = machineRows.reduce<TotalWinReconciliationReportRow>(
            (accumulator, row) => {
                Object.entries(row.metrics).forEach(([key, value]) => {
                    const metricKey = key as ReconciliationReportMetrics;
                    const currentValue = accumulator.metrics[metricKey] || 0;
                    accumulator.metrics[metricKey] = currentValue + (value || 0);
                });
                accumulator.variance = (accumulator.variance || 0) + (row.variance || 0);

                return accumulator;
            },
            { metrics: initialMetrics, variance: 0 }
        );

        return totalsRow;
    }

    private calculateVariance(metrics: MachineWinReconciliationReportMetrics): number | null {
        const accountingTotalWin = metrics[ReconciliationReportMetrics.AccountingTotalWin];
        const playTotalWin = metrics[ReconciliationReportMetrics.PlayTotalWin];
        const financeTotalWin = metrics[ReconciliationReportMetrics.FinanceTotalWin];

        const variance1 = accountingTotalWin && playTotalWin ? Math.abs(accountingTotalWin - playTotalWin) : 0;
        const variance2 = accountingTotalWin && financeTotalWin ? Math.abs(accountingTotalWin - financeTotalWin) : 0;
        const variance3 = playTotalWin && financeTotalWin ? Math.abs(playTotalWin - financeTotalWin) : 0;

        const variance = Math.max(variance1, variance2, variance3);
        return variance;
    }

    private determineMetricsGroups(): Record<ReconciliationReportMetrics, ReconciliationReportMetrics[]> {
        return {
            [ReconciliationReportMetrics.CashRevenue]: [ReconciliationReportMetrics.FinanceCashIn],
            [ReconciliationReportMetrics.TicketRevenue]: [ReconciliationReportMetrics.FinanceTicketsIn],
            [ReconciliationReportMetrics.TransferRevenue]: [ReconciliationReportMetrics.FinanceTransfersIn],
            [ReconciliationReportMetrics.TotalRevenue]: [ReconciliationReportMetrics.FinanceTotalIn],
            [ReconciliationReportMetrics.TicketExpenses]: [ReconciliationReportMetrics.FinanceTicketsOut],
            [ReconciliationReportMetrics.TransferExpenses]: [ReconciliationReportMetrics.FinanceTransfersOut],
            [ReconciliationReportMetrics.AccountingHandpays]: [ReconciliationReportMetrics.FinanceHandpays],
            [ReconciliationReportMetrics.AccountingTotalOut]: [ReconciliationReportMetrics.FinanceTotalOut],

            [ReconciliationReportMetrics.PlayTotalWin]: [ReconciliationReportMetrics.FinanceTotalWin],
            [ReconciliationReportMetrics.Turnover]: [ReconciliationReportMetrics.FinanceTotalWin],
            [ReconciliationReportMetrics.PaidToMachine]: [ReconciliationReportMetrics.FinanceTotalWin],
            [ReconciliationReportMetrics.Jackpots]: [ReconciliationReportMetrics.FinanceTotalWin],
            [ReconciliationReportMetrics.Progressives]: [ReconciliationReportMetrics.FinanceTotalWin]
        };
    }
}
