import { ReconciliationReportMetrics } from '@cms/machine-metrics';
import { Column, TgBrowserStorageColumnsSource } from '@tronius/frontend-ui';

import { ReconciliationReportMetricsPipePipe } from '../pipes/reconciliation-report-metrics.pipe';

export const machineColumns: Column[] = [
    {
        id: 'machine.location',
        name: $localize`Machine Location`,
        columnHeaderAlign: 'left',
        columnAlign: 'left',
        sortable: true
    },
    {
        id: 'machine.assetNumber',
        name: $localize`Asset Number`,
        columnHeaderAlign: 'right',
        columnAlign: 'right',
        sortable: true
    }
];

export const varianceColumn: Column = {
    id: 'variance',
    name: $localize`Variance`,
    type: 'currency',
    columnHeaderAlign: 'right',
    columnAlign: 'right',
    sortable: true
};

const metricToColumn = (metricKey: ReconciliationReportMetrics): Column => ({
    id: `metrics.${metricKey}`,
    name: ReconciliationReportMetricsPipePipe.stringToDescription(metricKey),
    type: 'currency',
    columnHeaderAlign: 'right' as const,
    columnAlign: 'right' as const
});

export const accountingTotalWinColumn: Column = metricToColumn(ReconciliationReportMetrics.AccountingTotalWin);
export const playTotalWinColumn: Column = metricToColumn(ReconciliationReportMetrics.PlayTotalWin);
export const financeTotalWinColumn: Column = metricToColumn(ReconciliationReportMetrics.FinanceTotalWin);

export const accountingMetricsColumns: Column[] = [
    ReconciliationReportMetrics.CashRevenue,
    ReconciliationReportMetrics.TicketRevenue,
    ReconciliationReportMetrics.TransferRevenue,
    ReconciliationReportMetrics.AccountingCreditsIn,
    ReconciliationReportMetrics.TotalRevenue,
    ReconciliationReportMetrics.TicketExpenses,
    ReconciliationReportMetrics.TransferExpenses,
    ReconciliationReportMetrics.AccountingHandpays,
    ReconciliationReportMetrics.AccountingCreditsOut,
    ReconciliationReportMetrics.AccountingTotalOut
].map(metricToColumn);

export const playMetricsColumns: Column[] = [
    ReconciliationReportMetrics.Turnover,
    ReconciliationReportMetrics.PaidToMachine,
    ReconciliationReportMetrics.Jackpots,
    ReconciliationReportMetrics.Progressives
].map(metricToColumn);

export const financeMetricsColumns: Column[] = [
    ReconciliationReportMetrics.FinanceCashIn,
    ReconciliationReportMetrics.FinanceTicketsIn,
    ReconciliationReportMetrics.FinanceTransfersIn,
    ReconciliationReportMetrics.FinanceCreditsIn,
    ReconciliationReportMetrics.FinanceTotalIn,
    ReconciliationReportMetrics.FinanceTicketsOut,
    ReconciliationReportMetrics.FinanceTransfersOut,
    ReconciliationReportMetrics.FinanceHandpays,
    ReconciliationReportMetrics.FinanceCreditsOut,
    ReconciliationReportMetrics.FinanceTotalOut
].map(metricToColumn);

const nonMachineFullColumns: Column[] = [
    accountingTotalWinColumn,
    ...accountingMetricsColumns,
    playTotalWinColumn,
    ...playMetricsColumns,
    financeTotalWinColumn,
    ...financeMetricsColumns,
    varianceColumn
];

const nonMachineWinColumns = [accountingTotalWinColumn, playTotalWinColumn, financeTotalWinColumn, varianceColumn];

export const getWinReconciliationReportColumnsSource = (
    timeInterval: 'daily' | 'hourly',
    columns: 'full' | 'win',
    table: 'machines' | 'total'
): TgBrowserStorageColumnsSource => {
    let columnList = columns === 'full' ? nonMachineFullColumns : nonMachineWinColumns;
    columnList = table === 'machines' ? [...machineColumns, ...columnList] : columnList;

    return new TgBrowserStorageColumnsSource(columnList, `win-reconciliation-report-${timeInterval}-${columns}-${table}`);
};
