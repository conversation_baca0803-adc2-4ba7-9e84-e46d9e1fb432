import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatIconModule } from '@angular/material/icon';
import { MachineWinReconciliationReportMetrics, ReconciliationReportMetrics } from '@cms/machine-metrics';
import { TgPrimitiveTextComponent } from '@tronius/frontend-ui';

import { ReconciliationAmountIconComponent } from './reconciliation-amount-icon.component';

describe('ReconciliationAmountIconComponent', () => {
    let component: ReconciliationAmountIconComponent;
    let fixture: ComponentFixture<ReconciliationAmountIconComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ReconciliationAmountIconComponent, MatIconModule, TgPrimitiveTextComponent]
        }).compileComponents();

        fixture = TestBed.createComponent(ReconciliationAmountIconComponent);
        component = fixture.componentInstance;
    });

    describe('isMetricMatching', () => {
        it('should return true when metric matches its group values', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: 1000
            };

            expect(component.isMetricMatching).toBe(true);
        });

        it('should return false when metric does not match its group values', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: 1500
            };

            expect(component.isMetricMatching).toBe(false);
        });

        it('should return true when metric has no group mapping', () => {
            component.metricKey = ReconciliationReportMetrics.AccountingTotalWin;
            component.metricValues = {
                [ReconciliationReportMetrics.AccountingTotalWin]: 1000
            };

            expect(component.isMetricMatching).toBe(true);
        });

        it('should return false when current metric value is null', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: null,
                [ReconciliationReportMetrics.FinanceCashIn]: 1000
            };

            expect(component.isMetricMatching).toBe(false);
        });

        it('should return false when group metric value is null', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: null
            };

            expect(component.isMetricMatching).toBe(false);
        });

        it('should handle multiple group metrics correctly', () => {
            component.metricKey = ReconciliationReportMetrics.PlayTotalWin;
            component.metricValues = {
                [ReconciliationReportMetrics.PlayTotalWin]: 500,
                [ReconciliationReportMetrics.FinanceTotalWin]: 500
            };

            expect(component.isMetricMatching).toBe(true);
        });
    });

    describe('template rendering', () => {
        it('should display green check icon when metric is matching', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: 1000
            };

            fixture.detectChanges();

            const icon = fixture.nativeElement.querySelector('mat-icon');
            expect(icon.textContent.trim()).toBe('check_circle');
            expect(icon.classList.contains('text-success')).toBe(true);
        });

        it('should display red error icon when metric is not matching', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: 1500
            };

            fixture.detectChanges();

            const icon = fixture.nativeElement.querySelector('mat-icon');
            expect(icon.textContent.trim()).toBe('error');
            expect(icon.classList.contains('text-danger')).toBe(true);
        });

        it('should display currency value using tg-primitive-text', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: 1000
            };

            fixture.detectChanges();

            const primitiveText = fixture.nativeElement.querySelector('tg-primitive-text');
            expect(primitiveText).toBeTruthy();
        });
    });
});
