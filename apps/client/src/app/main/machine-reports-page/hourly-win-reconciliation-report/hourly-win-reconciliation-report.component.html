<tg-main-panel [contentPadding]="true" [fullHeight]="true" [scrollable]="true">
    <form [formGroup]="form" class="no-print">
        <mat-form-field>
            <mat-label i18n>Type</mat-label>
            <mat-select formControlName="type">
                @for (reportType of reportTypesArray; track reportType) {
                    <mat-option [value]="reportType">{{ reportType | reportType }}</mat-option>
                }
            </mat-select>
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Promo</mat-label>
            <mat-select formControlName="promo">
                @for (promoInclusionType of promoInclusionTypes; track promoInclusionType) {
                    <mat-option [value]="promoInclusionType">{{ promoInclusionType | winReconciliationReportType }}</mat-option>
                }
            </mat-select>
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Gaming Day</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="gamingDay" placeholder="MM/DD/YYYY" i18n-placeholder required />
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            @if (form.controls.gamingDay.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            }
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Start Hour</mat-label>
            <mat-select formControlName="startHourIndex" (selectionChange)="form.controls.endHourIndex.updateValueAndValidity()" required>
                @for (hour of startHours; track hour.value) {
                    <mat-option [value]="hour.value">{{ hour.label }}</mat-option>
                }
            </mat-select>
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>End Hour</mat-label>
            <mat-select formControlName="endHourIndex" required>
                @for (hour of endHours; track hour.value) {
                    <mat-option [value]="hour.value" *ngIf="hour.value > startHourIndex">{{ hour.label }}</mat-option>
                }
            </mat-select>
            @if (form.controls.endHourIndex.errors?.greaterThanOtherControl?.otherControlName === 'startHourIndex') {
                <mat-error i18n>End hour should be greater than start hour</mat-error>
            }
        </mat-form-field>

        <button mat-flat-button color="primary" [disabled]="!form.valid" (click)="generateReport()" i18n>
            <mat-icon>assessment</mat-icon>
            Generate Report
        </button>

        <button mat-flat-button color="primary" (click)="print()"><mat-icon>print</mat-icon> <span i18n>Print</span></button>
    </form>

    <div *ngIf="requestData && requestData.gamingDay" id="print-hourly-win-reconciliation-report">
        <div class="header">
            <div i18n class="operator-row print-only">{{ operatorName }}</div>
            <div class="title-row print-only">
                <div class="title" i18n>Hourly Win Reconciliation Report</div>
                <div class="generated-meta-info-container">
                    <div i18n class="label">Generated by:</div>
                    <div class="operator-and-time">{{ generatedBy }}, {{ generatedTime | tgDateTime }}</div>
                </div>
            </div>
            <div class="filters-row">
                <div class="filters print-only">
                    <div class="label-value">
                        <div i18n class="label">Gaming Day:</div>
                        <div class="value">
                            {{ requestData.gamingDay | tgDate }}
                        </div>
                    </div>
                    <div class="label-value">
                        <div i18n class="label">Promo:</div>
                        <div class="value">{{ requestData.promo }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div *ngIf="requestData" id="print-report-hourly-metrics">
            <h4 i18n>
                Hourly Machine Performance {{ requestData.gamingDay | tgDate }} - from
                {{ startHours[requestData.startHourIndex!].label }} to
                {{ endHours[requestData.endHourIndex!].label }}
            </h4>
            @if (dataSourceTotal && columnsSourceTotal) {
                <h5 i18n>Total</h5>
                <div class="totals-section">
                    <tg-table
                        #table
                        [enableSelection]="false"
                        [columnsSource]="columnsSourceTotal"
                        [dataSource]="dataSourceTotal"
                        #sort="matSort"
                        [matSort]="sort"
                        [showPaginator]="false"
                        matSortActive="variance"
                        matSortDirection="desc"
                        matSortDisableClear
                    ></tg-table>
                </div>
            }
            @if (dataSourceMachines && columnsSourceMachines) {
                <h5 i18n>Machines</h5>
                <div class="machines-section">
                    <tg-table
                        #table
                        [enableSelection]="false"
                        [columnsSource]="columnsSourceMachines"
                        [dataSource]="dataSourceMachines"
                        #sort="matSort"
                        [matSort]="sort"
                        [showPaginator]="false"
                        matSortActive="variance"
                        matSortDirection="desc"
                        matSortDisableClear
                    >
                    </tg-table>
                </div>
            }
        </div>
    </div>
    <tg-loading-overlay [visible]="isLoading" [showSpinner]="true" style="z-index: 1000"></tg-loading-overlay>
</tg-main-panel>
