import { CommonModule } from '@angular/common';
import { Component, TemplateRef, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ErrorUtil, TgLoggerFactory } from '@tronius/frontend-common';
import {
    ConfirmationDialogComponent,
    TableRowMenuAction,
    TgFiltersModule,
    TgMainPanelModule,
    TgPagedDataSource,
    TgTableModule
} from '@tronius/frontend-ui';
import { ConnectionStatus } from '@tronius/shared-common';
import { CmsAction, MachineCurrent, MachineLockState, MachineStatus, OperationalStatus } from '@tronius/shared-domain';

import { MachineLockStatePipe } from './machine-lock-state/machine-lock-state.pipe';
import { defaultMachineSort } from './machine.util';
import { MachineLiveViewColumnsSource } from './machines-live-view.columns';
import { OperationalStatusPipe } from './operational-status/operational-status.pipe';
import { SelectOperationalStatusComponent } from './select-operational-status-dialog/select-operational-status-dialog.component';
import { SessionStatePipe } from './session-state/session-state.pipe';
import { MachineStatusPipe } from './status/machine-status.pipe';
import { ConnectionStatusPipe } from '../../../common/connection-status/connection-status.pipe';
import { BackendService } from '../../../core/backend/backend.service';
import { MachineCurrentService } from '../../../core/machine-current/machine-current.service';

@Component({
    selector: 'app-machines-live-view',
    standalone: true,
    imports: [
        CommonModule,
        ConnectionStatusPipe,
        MatButtonModule,
        MatDialogModule,
        MatIconModule,
        MatSnackBarModule,
        MatTableModule,
        MatTooltipModule,
        MachineStatusPipe,
        RouterModule,
        TgTableModule,
        TgMainPanelModule,
        TgFiltersModule,
        SessionStatePipe,
        OperationalStatusPipe,
        MachineLockStatePipe,
        ConfirmationDialogComponent
    ],
    templateUrl: './machines-live-view.component.html',
    styleUrls: ['./machines-live-view.component.scss']
})
export class MachinesLiveViewComponent {
    private static readonly logger = TgLoggerFactory.getLogger(MachinesLiveViewComponent.name);

    @ViewChild('dialogTemplate', { static: false }) dialogTemplate?: TemplateRef<ConfirmationDialogComponent>;

    columnsSource = new MachineLiveViewColumnsSource('machine-live-view');
    dataSource = new TgPagedDataSource<MachineCurrent>((request) => this.backendService.getMachineState(request), defaultMachineSort);

    protected actions: Array<TableRowMenuAction<MachineCurrent>> = this.createActions();

    constructor(
        private readonly route: ActivatedRoute,
        private readonly router: Router,
        private readonly backendService: BackendService,
        private readonly machineCurrentService: MachineCurrentService,
        private readonly snackBar: MatSnackBar,
        private readonly matDialog: MatDialog,
        private readonly dialog: MatDialog
    ) {}

    async resetHandpay(id: string): Promise<void> {
        try {
            await this.backendService.resetHandpay(id);
            this.snackBar.open($localize`Reset handpay request send to the machine.`, undefined, { duration: 5000 });
            // TODO maybe we should wait for an event indicating handpay reset successfully (status change or event).
        } catch (error) {
            MachinesLiveViewComponent.logger.error('Handpay action failed', ErrorUtil.extractErrorMessage(error));
            this.snackBar.open($localize`Failed to send reset handpay request to the machine.`, $localize`Close`);
        }
    }

    private createActions(): Array<TableRowMenuAction<MachineCurrent>> {
        return [
            {
                title: $localize`Manual funds transfer to machine`,
                action: (row: MachineCurrent) =>
                    // eslint-disable-next-line no-void
                    void this.router.navigate(['../..', 'cashier', 'aft-in'], {
                        queryParams: { machineId: row.id },
                        relativeTo: this.route
                    }),
                show: (row: MachineCurrent) =>
                    [MachineStatus.Available, MachineStatus.Busy].includes(row.status) &&
                    row.connectionStatus === ConnectionStatus.Connected
            },
            {
                title: $localize`Reset handpay`,
                icon: 'lock_reset',
                action: async (row: MachineCurrent) => this.resetHandpay(row.id),
                show: (row: MachineCurrent) => row.status === MachineStatus.Handpay && row.connectionStatus === ConnectionStatus.Connected
            },
            {
                title: $localize`Machine meters`,
                icon: 'analytics',
                action: (row: MachineCurrent) =>
                    // eslint-disable-next-line no-void
                    void this.router.navigate(['../..', 'meter', 'raw-current'], {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        queryParams: { 'filters-machine': `in;${row.id}` },
                        relativeTo: this.route
                    }),
                cmsPermissions: [CmsAction.MeterListRawCurrent]
            },
            {
                title: $localize`Machine events`,
                icon: 'notifications',
                action: (row: MachineCurrent) =>
                    // eslint-disable-next-line no-void
                    void this.router.navigate(['../..', 'events', 'events'], {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        queryParams: { 'filters-machineId': `in;${row.id}` },
                        relativeTo: this.route
                    }),
                cmsPermissions: [CmsAction.EventsList]
            },
            {
                title: $localize`Set Operational Status`,
                icon: 'flag',
                action: (row: MachineCurrent) => {
                    this.openSelectOperationalStatusDialog(row);
                },
                cmsPermissions: [CmsAction.MachineUpdateOperationalStatus]
            },
            {
                title: $localize`Lock Machine`,
                icon: 'lock',
                action: (row: MachineCurrent) => this.displayLockStateConfirmationDialog(row, MachineLockState.Locked),
                show: (row: MachineCurrent) => row.lockState === MachineLockState.Unlocked,
                cmsPermissions: [CmsAction.MachineUpdateLockState]
            },
            {
                title: $localize`Unlock Machine`,
                icon: 'lock_reset',
                action: (row: MachineCurrent) => this.displayLockStateConfirmationDialog(row, MachineLockState.Unlocked),
                show: (row: MachineCurrent) => row.lockState === MachineLockState.Locked,
                cmsPermissions: [CmsAction.MachineUpdateLockState]
            },
            {
                title: $localize`Reboot MED`,
                icon: 'restart_alt',
                fontSet: 'material-symbols-rounded-fill',
                show: (row: MachineCurrent) => !!(row.deviceId && row.device?.ipAddress),
                action: (row: MachineCurrent) => this.confirmRebootMed(row),
                cmsPermissions: [CmsAction.MachineReboot]
            },
            {
                title: $localize`Restart MED App`,
                icon: 'refresh',
                fontSet: 'material-symbols-rounded-fill',
                show: (row: MachineCurrent) => !!(row.deviceId && row.device?.ipAddress),
                action: (row: MachineCurrent) => this.confirmRestartMedApp(row),
                cmsPermissions: [CmsAction.MachineRestart]
            },
            {
                title: $localize`Ticket Operations`,
                icon: 'barcode',
                fontSet: 'material-symbols-rounded',
                action: (row: MachineCurrent) =>
                    // eslint-disable-next-line no-void
                    void this.router.navigate(['../..', 'tickets', 'operations'], {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        queryParams: { 'filters-triggeredById': `in;${row.id}` },
                        relativeTo: this.route
                    }),
                cmsPermissions: [CmsAction.TicketOperationList]
            },
            {
                title: $localize`Handpay Operations`,
                icon: 'attribution',
                fontSet: 'material-symbols-rounded-fill',
                action: (row: MachineCurrent) =>
                    // eslint-disable-next-line no-void
                    void this.router.navigate(['../..', 'handpays', 'operations'], {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        queryParams: { 'filters-triggeredById': `in;${row.id}` },
                        relativeTo: this.route
                    }),
                cmsPermissions: [CmsAction.HandpayOperationList]
            }
        ];
    }

    private openSelectOperationalStatusDialog(machineCurrent: MachineCurrent): void {
        const dialogRef = this.matDialog.open<SelectOperationalStatusComponent, MachineCurrent, OperationalStatus | undefined>(
            SelectOperationalStatusComponent,
            {
                data: machineCurrent
            }
        );

        dialogRef.afterClosed().subscribe((result: OperationalStatus | undefined) => {
            if (result) {
                this.updateMachineCurrentOperationalStatus(result, machineCurrent);
            }
        });
    }

    private updateMachineCurrentOperationalStatus(operationalStatus: OperationalStatus, rowMachineCurrent: MachineCurrent): void {
        this.backendService.updateMachineCurrentOperationalStatus(rowMachineCurrent.id, operationalStatus).subscribe({
            next: () => {
                void this.dataSource.reload();
                this.snackBar.open($localize`Machine Operational Status updated`, undefined, { duration: 5000 });
            },
            error: (error: unknown) => {
                MachinesLiveViewComponent.logger.error('Machine Operational Status update failed', ErrorUtil.extractErrorMessage(error));
                this.snackBar.open($localize`Machine Operational Status update failed`, $localize`Close`);
            }
        });
    }

    private displayLockStateConfirmationDialog(machineCurrent: MachineCurrent, lockState: MachineLockState): void {
        if (this.dialogTemplate) {
            this.dialog
                .open(this.dialogTemplate)
                .afterClosed()
                .subscribe((result) => {
                    if (result) {
                        this.updateMachineCurrentLockState(machineCurrent, lockState);
                    }
                });
        }
    }

    private updateMachineCurrentLockState(machineCurrent: MachineCurrent, lockState: MachineLockState): void {
        this.backendService.updateMachineCurrentLockState(machineCurrent.id, lockState).subscribe({
            next: () => {
                // eslint-disable-next-line @typescript-eslint/no-floating-promises
                this.dataSource.reload();
                this.snackBar.open($localize`Machine Lock State updated`, undefined, { duration: 5000 });
            },
            error: (error: unknown) => {
                MachinesLiveViewComponent.logger.error('Machine Lock State update failed', ErrorUtil.extractErrorMessage(error));
                this.snackBar.open($localize`Machine Lock State update failed`, $localize`Close`);
            }
        });
    }

    private confirmRebootMed(machineCurrent: MachineCurrent): void {
        const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            data: {
                title: $localize`Confirm Reboot MED`,
                content: $localize`Do you want to reboot the MED for machine "${machineCurrent.machine?.location}"?`
            }
        });

        dialogRef.afterClosed().subscribe((result) => {
            if (result) {
                this.rebootMed(machineCurrent);
            }
        });
    }

    private confirmRestartMedApp(machineCurrent: MachineCurrent): void {
        const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            data: {
                title: $localize`Confirm Restart MED App`,
                content: $localize`Do you want to restart the MED application for machine "${machineCurrent.machine?.location}"?`
            }
        });

        dialogRef.afterClosed().subscribe((result) => {
            if (result) {
                this.restartMedApp(machineCurrent);
            }
        });
    }

    private rebootMed(machineCurrent: MachineCurrent): void {
        this.machineCurrentService.rebootMed(machineCurrent.id).subscribe({
            next: () => {
                this.snackBar.open($localize`MED reboot command sent successfully.`, undefined, {
                    duration: 5000
                });
                void this.dataSource.reload();
            },
            error: (error) => {
                MachinesLiveViewComponent.logger.error('Reboot MED action failed', ErrorUtil.extractErrorMessage(error));
                this.snackBar.open($localize`Failed to send reboot MED command.`, $localize`Close`);
            }
        });
    }

    private restartMedApp(machineCurrent: MachineCurrent): void {
        this.machineCurrentService.restartMedApp(machineCurrent.id).subscribe({
            next: () => {
                this.snackBar.open($localize`MED app restart command sent successfully.`, undefined, {
                    duration: 5000
                });
                void this.dataSource.reload();
            },
            error: (error) => {
                MachinesLiveViewComponent.logger.error('Restart MED app action failed', ErrorUtil.extractErrorMessage(error));
                this.snackBar.open($localize`Failed to send restart MED app command.`, $localize`Close`);
            }
        });
    }
}
